package ltmdb

import (
	"time"

	"github.com/blagojts/viper"
	"github.com/spf13/pflag"
	"github.com/timescale/tsbs/pkg/data/serialize"
	"github.com/timescale/tsbs/pkg/data/source"
	"github.com/timescale/tsbs/pkg/targets"
	"github.com/timescale/tsbs/pkg/targets/constants"
)

func NewTarget() targets.ImplementedTarget {
	return &ltmdbTarget{}
}

type ltmdbTarget struct {
}

func (t *ltmdbTarget) TargetName() string {
	return constants.FormatLTMDB
}

func (t *ltmdbTarget) Serializer() serialize.PointSerializer {
	return &Serializer{}
}

func (t *ltmdbTarget) Benchmark(
	targetDB string, dataSourceConfig *source.DataSourceConfig, v *viper.Viper,
) (targets.Benchmark, error) {
	var loadingOptions LoadingOptions
	if err := v.Unmarshal(&loadingOptions); err != nil {
		return nil, err
	}
	return NewBenchmark(targetDB, &loadingOptions, dataSourceConfig)
}

func (t *ltmdbTarget) TargetSpecificFlags(flagPrefix string, flagSet *pflag.FlagSet) {
	flagSet.String(flagPrefix+"host", "localhost", "Hostname of LTMDB instance")
	flagSet.String(flagPrefix+"port", "3306", "Which port to connect to on the database host")
	flagSet.String(flagPrefix+"user", "root", "User to connect to LTMDB as")
	flagSet.String(flagPrefix+"pass", "", "Password for user connecting to LTMDB (leave blank if not password protected)")

	flagSet.Bool(flagPrefix+"log-batches", false, "Whether to time individual batches.")

	flagSet.Bool(flagPrefix+"use-jsonb-tags", false, "Whether tags should be stored as JSONB (instead of a separate table with schema)")
	flagSet.Bool(flagPrefix+"in-table-partition-tag", false, "Whether the partition key (e.g. hostname) should also be in the metrics hypertable")

	flagSet.Int(flagPrefix+"replication-factor", 0, "Setting replication factor >= 1 will create a distributed hypertable")
	flagSet.Int(flagPrefix+"partitions", 0, "Number of partitions")
	flagSet.Duration(flagPrefix+"chunk-time", 12*time.Hour, "Duration that each chunk should represent, e.g., 12h")

	flagSet.Bool(flagPrefix+"time-index", true, "Whether to build an index on the time dimension")
	flagSet.Bool(flagPrefix+"time-partition-index", false, "Whether to build an index on the time dimension, compounded with partition")
	flagSet.Bool(flagPrefix+"partition-index", true, "Whether to build an index on the partition key")
	flagSet.String(flagPrefix+"field-index", ValueTimeIdx, "index types for tags (comma delimited)")
	flagSet.Int(flagPrefix+"field-index-count", 0, "Number of indexed fields (-1 for all)")

	flagSet.String(flagPrefix+"write-profile", "", "File to output CPU/memory profile to")
	flagSet.String(flagPrefix+"write-replication-stats", "", "File to output replication stats to")
	flagSet.Bool(flagPrefix+"create-metrics-table", true, "Drops existing and creates new metrics table. Can be used for both regular and hypertable")
	flagSet.String(flagPrefix+"storage-engine", "kmemstore", "Storage engine for tables (kmemstore or innodb)")
	flagSet.Bool(flagPrefix+"ltmdb-as-pure-duck", false, "Whether to use ltmdb in pure DuckDB mode")

	flagSet.Bool(flagPrefix+"force-text-format", false, "Send/receive data in text format")
}
