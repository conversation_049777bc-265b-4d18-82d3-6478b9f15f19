package ltmdb

import (
	"database/sql"
	"fmt"
	"log"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"

	"github.com/timescale/tsbs/pkg/targets"
)

const (
	tagsKey      = "tags"
	TimeValueIdx = "TIME-VALUE"
	ValueTimeIdx = "VALUE-TIME"
)

// allows for testing
var fatal = log.Fatalf

var tableCols = make(map[string][]string)

type dbCreator struct {
	driver  string
	ds      targets.DataSource
	connStr string
	opts    *LoadingOptions
}

func (d *dbCreator) Init() {
	// read the headers before all else
	d.ds.Headers()
	d.initConnectString()
}

func (d *dbCreator) initConnectString() {
	// For MySQL, we don't need to modify the connection string for admin operations
	// as we can connect to the default database (usually mysql) to create/drop databases
	d.connStr = fmt.Sprintf("%s:%s@tcp(%s:%s)/", d.opts.User, d.opts.Pass, d.opts.Host, d.opts.Port)
}

// MustConnect connects or exits on errors
func MustConnect(dbType, connStr string) *sql.DB {
	db, err := sql.Open(dbType, connStr)
	if err != nil {
		panic(err)
	}
	return db
}

func (d *dbCreator) DBExists(dbName string) bool {
	db := MustConnect(d.driver, d.connStr)
	defer db.Close()
	var r *sql.Rows
	if d.opts.PureDuckDB {
		// TODO() duckdb_sql maybe changed, we need find a robust way to get catalog name
		r = MustQuery(db, "/*+ duck_execute */ SELECT SCHEMA_NAME FROM duckdb_sql.INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '"+dbName+"'")
	} else {
		r = MustQuery(db, "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?", dbName)
	}
	defer r.Close()
	return r.Next()
}

func (d *dbCreator) RemoveOldDB(dbName string) error {
	db := MustConnect(d.driver, d.connStr)
	defer db.Close()
	if d.opts.PureDuckDB {
		MustExec(db, "/*+ duck_execute */ DROP SCHEMA IF EXISTS "+dbName+" CASCADE")
	} else {
		MustExec(db, "DROP DATABASE IF EXISTS "+dbName)
	}
	return nil
}

func (d *dbCreator) CreateDB(dbName string) error {
	db := MustConnect(d.driver, d.connStr)
	if d.opts.PureDuckDB {
		MustExec(db, "/*+ duck_execute */ CREATE SCHEMA "+dbName)
	} else {
		MustExec(db, "CREATE DATABASE "+dbName)
	}
	db.Close()
	return nil
}

func (d *dbCreator) PostCreateDB(dbName string) error {
	var dbBench *sql.DB
	if d.opts.PureDuckDB {
		dbBench = MustConnect(d.driver, d.connStr)
		MustExec(dbBench, "/*+ duck_execute */ USE "+dbName)
	} else {
		dbBench = MustConnect(d.driver, d.opts.GetConnectString(dbName))
	}
	defer dbBench.Close()

	headers := d.ds.Headers()
	tagNames := headers.TagKeys
	tagTypes := headers.TagTypes
	if d.opts.CreateMetricsTable {
		createTagsTable(dbBench, tagNames, tagTypes, d.opts.UseJSON, d.opts.StorageEngine, d.opts.PureDuckDB)
	}
	// tableCols is a global map. Globally cache the available tags
	tableCols[tagsKey] = tagNames
	// tagTypes holds the type of each tag value (as strings from Go types (string, float32...))
	d.opts.TagColumnTypes = tagTypes

	// Each table is defined in the dbCreator 'cols' list. The definition consists of a
	// comma separated list of the table name followed by its columns. Iterate over each
	// definition to update our global cache and create the requisite tables and indexes
	for tableName, columns := range headers.FieldKeys {
		// tableCols is a global map. Globally cache the available columns for the given table
		tableCols[tableName] = columns
		fieldDefs, indexDefs := d.getFieldAndIndexDefinitions(tableName, columns)
		if d.opts.CreateMetricsTable {
			d.createTableAndIndexes(dbBench, tableName, fieldDefs, indexDefs)
		} else {
			if d.opts.PureDuckDB {
				panic("Pure DuckDB mode is not supported when not creating the metrics table")
			}
			// If not creating table, wait for another client to set it up
			i := 0
			checkTableQuery := fmt.Sprintf("SELECT * FROM information_schema.tables WHERE table_schema = %s AND table_name = '%s'", dbName, tableName)
			r := MustQuery(dbBench, checkTableQuery)
			for !r.Next() {
				time.Sleep(100 * time.Millisecond)
				i += 1
				if i == 600 {
					return fmt.Errorf("expected table not created after one minute of waiting")
				}
				r = MustQuery(dbBench, checkTableQuery)
			}
		}
	}
	return nil
}

// getFieldAndIndexDefinitions iterates over a list of table columns, populating lists of
// definitions for each desired field and index. Returns separate lists of fieldDefs and indexDefs
func (d *dbCreator) getFieldAndIndexDefinitions(tableName string, columns []string) ([]string, []string) {
	var fieldDefs []string
	var indexDefs []string
	var allCols []string

	partitioningField := tableCols[tagsKey][0]
	// If the user has specified that we should partition on the primary tags key, we
	// add that to the list of columns to create
	if d.opts.InTableTag {
		allCols = append(allCols, partitioningField)
	}

	allCols = append(allCols, columns...)
	extraCols := 0 // set to 1 when hostname is kept in-table
	for idx, field := range allCols {
		if len(field) == 0 {
			continue
		}
		fieldType := "DOUBLE PRECISION"
		idxType := d.opts.FieldIndex
		// This condition handles the case where we keep the primary tag key in the table
		// and partition on it. Since under the current implementation this tag is always
		// hostname, we set it to a TEXT field instead of DOUBLE PRECISION
		if d.opts.InTableTag && idx == 0 {
			fieldType = "TEXT"
			idxType = ""
			extraCols = 1
		}

		fieldDefs = append(fieldDefs, fmt.Sprintf("%s %s", field, fieldType))
		// If the user specifies indexes on additional fields, add them to
		// our index definitions until we've reached the desired number of indexes
		if d.opts.FieldIndexCount == -1 || idx < (d.opts.FieldIndexCount+extraCols) {
			indexDefs = append(indexDefs, d.getCreateIndexOnFieldCmds(tableName, field, idxType)...)
		}
	}
	return fieldDefs, indexDefs
}

// createTableAndIndexes takes a list of field and index definitions for a given tableName and constructs
// the necessary table, index, and potential hypertable based on the user's settings
func (d *dbCreator) createTableAndIndexes(dbBench *sql.DB, tableName string, fieldDefs []string, indexDefs []string) {
	// We default to the tags_id column unless users are creating the
	// name/hostname column in the time-series table for multi-node
	// testing. For distributed queries, pushdown of JOINs is not yet
	// supported.
	var partitionColumn string = "tags_id"

	if d.opts.InTableTag {
		partitionColumn = tableCols[tagsKey][0]
	}
	var hint string
	if d.opts.PureDuckDB {
		hint = "/*+ duck_execute */ "
	}
	MustExec(dbBench, fmt.Sprintf("%sDROP TABLE IF EXISTS %s", hint, tableName))
	if d.opts.PureDuckDB {
		MustExec(dbBench, fmt.Sprintf("/*+ duck_execute */ CREATE TABLE %s (time timestamp, tags_id integer, %s, additional_tags VARCHAR(1) DEFAULT NULL)", tableName, strings.Join(fieldDefs, ",")))
	} else {
		MustExec(dbBench, fmt.Sprintf("CREATE TABLE %s (time timestamp, tags_id integer, %s, additional_tags VARCHAR(1) DEFAULT NULL, primary key(tags_id, time)) ENGINE='%s'", tableName, strings.Join(fieldDefs, ","), d.opts.StorageEngine))
	}
	if d.opts.PartitionIndex {
		MustExec(dbBench, fmt.Sprintf("%sCREATE INDEX %s_idx_01 ON %s(%s, time)", hint, tableName, tableName, partitionColumn))
	}

	// Only allow one or the other, it's probably never right to have both.
	// Experimentation suggests (so far) that for 100k devices it is better to
	// use --time-partition-index for reduced index lock contention.
	if d.opts.TimePartitionIndex {
		MustExec(dbBench, fmt.Sprintf("%sCREATE INDEX %s_idx_02 ON %s(time, %s)", hint, tableName, tableName, partitionColumn))
	} else if d.opts.TimeIndex {
		MustExec(dbBench, fmt.Sprintf("%sCREATE INDEX %s_idx_02 ON %s(time)", hint, tableName, tableName))
	}

	MustExec(dbBench, fmt.Sprintf("%sCREATE UNIQUE INDEX %s_uidx_03 ON %s(tags_id, time, usage_user)", hint, tableName, tableName))
	MustExec(dbBench, fmt.Sprintf("%sCREATE UNIQUE INDEX %s_uidx_04 ON %s(time, tags_id, usage_user)", hint, tableName, tableName))

	for _, indexDef := range indexDefs {
		MustExec(dbBench, fmt.Sprintf("%s%s", hint, indexDef))
	}
}

func (d *dbCreator) getCreateIndexOnFieldCmds(hypertable, field, idxType string) []string {
	var ret []string
	var hint string
	if d.opts.PureDuckDB {
		hint = "/*+ duck_execute */ "
	}
	for _, idx := range strings.Split(idxType, ",") {
		if idx == "" {
			continue
		}

		indexDef := ""
		switch idx {
		case TimeValueIdx:
			indexDef = fmt.Sprintf("(time, %s)", field)
		case ValueTimeIdx:
			indexDef = fmt.Sprintf("(%s, time)", field)
		default:
			fatal("Unknown index type %v", idx)
		}

		ret = append(ret, fmt.Sprintf("%sCREATE INDEX %s_idx_03 ON %s %s", hint, hypertable, hypertable, indexDef))
	}
	return ret
}

func createTagsTable(db *sql.DB, tagNames, tagTypes []string, useJSON bool, storageEngine string, pureDuckDB bool) {
	var hint string
	if pureDuckDB {
		hint = "/*+ duck_execute */ "
		MustExec(db, "/*+ duck_execute */ DROP SEQUENCE IF EXISTS tags_id_seq")
	}
	MustExec(db, hint+"DROP TABLE IF EXISTS tags")
	if useJSON {
		// MustExec(db, "CREATE TABLE tags(id SERIAL PRIMARY KEY, tagset JSONB) STORAGE='kmemstore'")
		// MustExec(db, "CREATE UNIQUE INDEX uniq1 ON tags(tagset)")
		// MustExec(db, "CREATE INDEX idxginp ON tags USING gin (tagset jsonb_path_ops);")
		// return
		panic("JSONB is not supported, please set use-jsonb-tags to false")
	}

	if pureDuckDB {
		MustExec(db, "/*+ duck_execute */ CREATE SEQUENCE tags_id_seq START 1")
	}

	MustExec(db, generateTagsTableQuery(tagNames, tagTypes, storageEngine, pureDuckDB))
	MustExec(db, fmt.Sprintf("%s CREATE UNIQUE INDEX tags_uidx_001 ON tags(%s)", hint, strings.Join(tagNames, ",")))
	MustExec(db, fmt.Sprintf("%s CREATE UNIQUE INDEX tags_uidx_002 ON tags(%s)", hint, tagNames[0]))
}

func generateTagsTableQuery(tagNames, tagTypes []string, storageEngine string, pureDuckDB bool) string {
	tagColumnDefinitions := make([]string, len(tagNames))
	for i, tagName := range tagNames {
		mysqlType := serializedTypeToMySQLType(tagTypes[i])
		tagColumnDefinitions[i] = fmt.Sprintf("%s %s", tagName, mysqlType)
	}

	cols := strings.Join(tagColumnDefinitions, ", ")
	if pureDuckDB {
		return fmt.Sprintf("/*+ duck_execute */ CREATE TABLE tags(id BIGINT PRIMARY KEY DEFAULT nextval('tags_id_seq'), %s)", cols)
	}
	return fmt.Sprintf("CREATE TABLE tags(id SERIAL PRIMARY KEY, %s) ENGINE='%s'", cols, storageEngine)
}

func extractTagNamesAndTypes(tags []string) ([]string, []string) {
	tagNames := make([]string, len(tags))
	tagTypes := make([]string, len(tags))
	for i, tagWithType := range tags {
		tagAndType := strings.Split(tagWithType, " ")
		if len(tagAndType) != 2 {
			panic("tag header has invalid format")
		}
		tagNames[i] = tagAndType[0]
		tagTypes[i] = tagAndType[1]
	}

	return tagNames, tagTypes
}

// MustExec executes query or exits on error
func MustExec(db *sql.DB, query string, args ...interface{}) sql.Result {
	r, err := db.Exec(query, args...)
	if err != nil {
		fmt.Printf("could not execute sql: %s", query)
		panic(err)
	}
	return r
}

// MustQuery executes query or exits on error
func MustQuery(db *sql.DB, query string, args ...interface{}) *sql.Rows {
	r, err := db.Query(query, args...)
	if err != nil {
		panic(err)
	}
	return r
}

// MustBegin starts transaction or exits on error
func MustBegin(db *sql.DB) *sql.Tx {
	tx, err := db.Begin()
	if err != nil {
		panic(err)
	}
	return tx
}

func serializedTypeToMySQLType(serializedType string) string {
	switch serializedType {
	case "string":
		return "VARCHAR(70)"
	case "float32":
		return "FLOAT"
	case "float64":
		return "DOUBLE PRECISION"
	case "int64":
		return "BIGINT"
	case "int32":
		return "INTEGER"
	default:
		panic(fmt.Sprintf("unrecognized type %s", serializedType))
	}
}
