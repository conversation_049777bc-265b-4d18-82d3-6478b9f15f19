package ltmdb

import (
	"fmt"
	"time"
)

// Loading option vars:
type LoadingOptions struct {
	Host string `yaml:"host"`
	User string
	Pass string
	Port string

	LogBatches bool `yaml:"log-batches" mapstructure:"log-batches"`
	UseJSON    bool `yaml:"use-jsonb-tags" mapstructure:"use-jsonb-tags"`
	InTableTag bool `yaml:"in-table-partition-tag" mapstructure:"in-table-partition-tag"`

	NumberPartitions  int           `yaml:"partitions" mapstructure:"partitions"`
	PartitionColumn   string        `yaml:"partition-column" mapstructure:"partition-column"`
	ReplicationFactor int           `yaml:"replication-factor" mapstructure:"replication-factor"`
	ChunkTime         time.Duration `yaml:"chunk-time" mapstructure:"chunk-time"`

	TimeIndex          bool   `yaml:"time-index" mapstructure:"time-index"`
	TimePartitionIndex bool   `yaml:"time-partition-index" mapstructure:"time-partition-index"`
	PartitionIndex     bool   `yaml:"partition-index" mapstructure:"partition-index"`
	FieldIndex         string `yaml:"field-index" mapstructure:"field-index"`
	FieldIndexCount    int    `yaml:"field-index-count" mapstructure:"field-index-count"`

	ProfileFile          string `yaml:"write-profile" mapstructure:"write-profile"`
	ReplicationStatsFile string `yaml:"write-replication-stats" mapstructure:"write-replication-stats"`

	CreateMetricsTable bool     `yaml:"create-metrics-table" mapstructure:"create-metrics-table"`
	TagColumnTypes     []string `yaml:",omitempty" mapstructure:",omitempty"`
	StorageEngine      string   `yaml:"storage-engine" mapstructure:"storage-engine"`

	PureDuckDB bool `yaml:"pure-duckdb" mapstructure:"pure-duckdb"`
}

func (o *LoadingOptions) GetConnectString(dbName string) string {
	// Build MySQL connection string
	connectString := fmt.Sprintf("%s:%s@tcp(%s", o.User, o.Pass, o.Host)

	// Add port if specified
	if len(o.Port) > 0 {
		connectString = fmt.Sprintf("%s:%s", connectString, o.Port)
	}

	// Add database name and parameters
	connectString = fmt.Sprintf("%s)/%s?parseTime=true", connectString, dbName)

	return connectString
}

// ValidateStorageEngine validates the storage engine configuration
func (o *LoadingOptions) ValidateStorageEngine() error {
	if o.StorageEngine == "" {
		o.StorageEngine = "kmemstore" // Set default value
	}

	switch o.StorageEngine {
	case "kmemstore", "innodb":
		return nil
	default:
		return fmt.Errorf("invalid storage engine '%s', must be 'kmemstore' or 'innodb'", o.StorageEngine)
	}
}
