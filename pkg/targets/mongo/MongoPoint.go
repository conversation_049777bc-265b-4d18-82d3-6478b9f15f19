// automatically generated by the FlatBuffers compiler, do not modify

package mongo

import (
	flatbuffers "github.com/google/flatbuffers/go"
)

type MongoPoint struct {
	_tab flatbuffers.Table
}

func GetRootAsMongoPoint(buf []byte, offset flatbuffers.UOffsetT) *MongoPoint {
	n := flatbuffers.GetUOffsetT(buf[offset:])
	x := &MongoPoint{}
	x.Init(buf, n+offset)
	return x
}

func (rcv *MongoPoint) Init(buf []byte, i flatbuffers.UOffsetT) {
	rcv._tab.Bytes = buf
	rcv._tab.Pos = i
}

func (rcv *MongoPoint) Table() flatbuffers.Table {
	return rcv._tab
}

func (rcv *MongoPoint) MeasurementName() []byte {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(4))
	if o != 0 {
		return rcv._tab.ByteVector(o + rcv._tab.Pos)
	}
	return nil
}

func (rcv *MongoPoint) Timestamp() int64 {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(6))
	if o != 0 {
		return rcv._tab.GetInt64(o + rcv._tab.Pos)
	}
	return 0
}

func (rcv *MongoPoint) MutateTimestamp(n int64) bool {
	return rcv._tab.MutateInt64Slot(6, n)
}

func (rcv *MongoPoint) Tags(obj *MongoTag, j int) bool {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(8))
	if o != 0 {
		x := rcv._tab.Vector(o)
		x += flatbuffers.UOffsetT(j) * 4
		x = rcv._tab.Indirect(x)
		obj.Init(rcv._tab.Bytes, x)
		return true
	}
	return false
}

func (rcv *MongoPoint) TagsLength() int {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(8))
	if o != 0 {
		return rcv._tab.VectorLen(o)
	}
	return 0
}

func (rcv *MongoPoint) Fields(obj *MongoReading, j int) bool {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(10))
	if o != 0 {
		x := rcv._tab.Vector(o)
		x += flatbuffers.UOffsetT(j) * 4
		x = rcv._tab.Indirect(x)
		obj.Init(rcv._tab.Bytes, x)
		return true
	}
	return false
}

func (rcv *MongoPoint) FieldsLength() int {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(10))
	if o != 0 {
		return rcv._tab.VectorLen(o)
	}
	return 0
}

func MongoPointStart(builder *flatbuffers.Builder) {
	builder.StartObject(4)
}
func MongoPointAddMeasurementName(builder *flatbuffers.Builder, measurementName flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(0, flatbuffers.UOffsetT(measurementName), 0)
}
func MongoPointAddTimestamp(builder *flatbuffers.Builder, timestamp int64) {
	builder.PrependInt64Slot(1, timestamp, 0)
}
func MongoPointAddTags(builder *flatbuffers.Builder, tags flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(2, flatbuffers.UOffsetT(tags), 0)
}
func MongoPointStartTagsVector(builder *flatbuffers.Builder, numElems int) flatbuffers.UOffsetT {
	return builder.StartVector(4, numElems, 4)
}
func MongoPointAddFields(builder *flatbuffers.Builder, fields flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(3, flatbuffers.UOffsetT(fields), 0)
}
func MongoPointStartFieldsVector(builder *flatbuffers.Builder, numElems int) flatbuffers.UOffsetT {
	return builder.StartVector(4, numElems, 4)
}
func MongoPointEnd(builder *flatbuffers.Builder) flatbuffers.UOffsetT {
	return builder.EndObject()
}
