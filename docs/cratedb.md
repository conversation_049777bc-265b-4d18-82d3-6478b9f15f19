# TSBS Supplemental Guide: CrateDB

CrateDB is a distributed SQL database ideal for IoT & industrial time series
data. This supplemental guide explains how the data generated for TSBS is stored,
additional flags available when using the data importer (`tsbs_load_cratedb`),
and additional flags available for the query runner (`tsbs_run_queries_cratedb`).

**This should be read *after* the main README.**

## Data format

Data generated by `tsbs_generate_data` for CrateDB is a "pseudo-TSV" format.

A file with generated data consists of the header and measurement records that
are separated from the header by an empty line. Each record represented by one
line with tab-separated elements:

An example of a header for the `cpu-only` use case:

```text
tags,hostname,region,datacenter,rack,os,arch,team,service,service_version,service_environment
cpu,usage_user,usage_system,usage_idle,usage_nice,usage_iowait,usage_irq,usage_softirq,usage_steal,usage_guest,usage_guest_nice
```

A single record consists of the table name, a JSON representation of tags,
timestamp, and rest measurement values. For instance, a partial example of a
single record for the `cpu-only` use case:

```text
cpu\t{"hostname":"host_0","region":"eu-central-1",...}\t1451606400000000000\t58\t2\t24\t...
```

---

## `tsbs_load_cratedb` Additional Flags

### Database related

#### `-replicas` (type: `int`, default: `0`)

The number of replicas per a measurement table.

#### `-shards` (type: `int`, default: `5`)

The number of shards per a measurement table.

#### `-hosts` (type: `string`, default: `locahost`)

A comma-separated list of hostname of the nodes in the cluster.

#### `-port` (type: `int`, default: `5432`)

A port to connect to database instances.

#### `-user` (type: `string`, default: `crate`)

A user to connect to a CrateDB cluster.

#### `-pass` (type: `string`, default: ``)

A password for the user of a CrateDB cluster.

A port to connect to database instances.
---

## `tsbs_run_queries_crate` Additional Flags

### Database related

#### `-hosts` (type: `string`, default: `locahost`)

A comma-separated list of hostname of the nodes in the cluster.

#### `-port` (type: `int`, default: `5432`)

A port to connect to database instances.

#### `-user` (type: `string`, default: `crate`)

A user to connect to a CrateDB cluster.

#### `-pass` (type: `string`, default: ``)

A password for the user of a CrateDB cluster.

#### `-show-explain` (type: `boolean`, default: `false`)

Set to print out a plan for a query.
