# TSBS Supplemental Guide: InfluxDB

InfluxDB is a purpose-built time-series database written in Go from
InfluxData. This supplemental guide explains how
the data generated for TSBS is stored, additional flags available when
using the data importer (`tsbs_load_influx`), and additional flags
available for the query runner (`tsbs_run_queries_influx`). **This
should be read *after* the main README.**

## Data format

Data generated by `tsbs_generate_data` for InfluxDB is serialized in a
"pseudo-CSV" format. Each reading is composed of a single line where
the name of the table is the first item, a comma, followed by several
comma-separated items of tags in the format of `<label>=<value>`, a space,
several comma-separated items of fields in the format of `<label>=<value>`,
a space, and finally the timestamp for the reading.

An example for the `cpu-only` use case:
```text
cpu,hostname=host_0,region=eu-central-1,datacenter=eu-central-1b,rack=21,os=Ubuntu15.10,arch=x86,team=SF,service=6,service_version=0,service_environment=test usage_user=58.1317132304976170,usage_system=2.6224297271376256,usage_idle=24.9969495069947882,usage_nice=61.5854484633778867,usage_iowait=22.9481393231639395,usage_irq=63.6499207106198313,usage_softirq=6.4098777048301052,usage_steal=44.8799140503027445,usage_guest=80.5028770761136201,usage_guest_nice=38.2431182911542820 1451606400000000000
```

---

## `tsbs_load_influx` Additional Flags

### Database related

#### `-consistency` (type: `string`, default: `all`)

Consistency level for writes to the database. Options are `all`, `any`, `one`,
or `quorum`. Only applies for the clustered version.

#### `-replication-factor` (type: `int`, default: `1`)

Level of replication for each write, i.e., number of nodes to store the
data on. Only applies for the clustered version.

#### `-urls` (type: `string`, default: `http://localhost:8086`)

Comma-separated list of URLs to connect to for inserting data. Workers will be
distributed in a round robin fashion across the URLs.

### Miscellaneous

#### `-backoff` (type: `duration`, default: `1s`)

The amount of time per retry attempt when the server says it is too busy. A
longer backoff will potentially reduce write performance by waiting too long to
retry, leaving the system idle. It is expressed as a Golang time.Duration
string, meaning a number followed by a unit abbreviation (s = seconds,
m = minutes, h = hours), e.g., the default `1s` is one second.

#### `-gzip` (type: `boolean`, default: `true`)

Whether to encode writes to the server with gzip. For best performance, encoding
with gzip is the best choice, but if the server does not support or has gzip
disabled, this flag should be set to false.

---

## `tsbs_run_queries_influx` Additional Flags

### Database related

#### `-chunk-response-size` (type: `int`, default: `0`)

Number of series to return per response per query. If the query would generate
a response that is very large, it could cause the server to crash with
out-of-memory problems. This flag will chunk the response into multiple smaller
responses to prevent the server from crashing. The default of 0 will return
everything in a single response.

#### `-urls` (type: `string`, default: `http://localhost:8086`)

Comma-separated list of URLs to connect to for querying. Workers will be
distributed in a round robin fashion across the URLs.
