# TSBS Supplemental Guide: MongoDB

MongoDB is a general NoSQL database that stores data as JSON-like documents.
This supplemental guide explains how the data generated for TSBS is stored, additional flags available when
using the data importer (`tsbs_load_mongo`), and additional flags
available for the query runner (`tsbs_run_queries_mongo`). **This
should be read *after* the main README.**

## Data format

Data generated by `tsbs_generate_data` for MongoDB is serialized as a
FlatBuffer to represent each reading. This format is not (easily) human readable
in its serialized format, however the FlatBuffer is specified as follows:
```text
// mongo.fbs
namespace serialize;
table MongoTag {
  key:string;
  value:string;
}

table MongoReading {
  key:string;
  value:double;
}

table MongoPoint {
  measurementName:string;
  timestamp:long;
  tags:[MongoTag];
  fields:[MongoReading];
}

root_type MongoPoint;
```

---

## `tsbs_load_mongo` Additional Flags

### Database related

#### `-url` (type: `string`, default: `localhost:27017`)

URL for connecting to the MongoDB server daemon.

#### `-write-timeout` (type: `duration`, default: `10s`)

Length of the timeout for writes.
It is expressed as a Golang time.Duration string, meaning a number followed
by a unit abbreviation (s = seconds,
m = minutes, h = hours), e.g., the default `10s` is ten seconds.


### Miscellaneous

#### `-document-per-event` (type: `boolean`, default: `false`)

Store each data reading as a separate document instead of the default aggregated
format. The default aggregated format stores an hour's worth of readings for
a particular device in one document and uses updates for a more efficient
storage model. However for testing or comparing, this flag is provided to use
a model where each data reading is stored as a single document.

---

## `tsbs_run_queries_mongo` Additional Flags

### Database related

#### `-url` (type: `string`, default: `localhost:27017`)

URL for connecting to the MongoDB server daemon.

#### `-read-timeout` (type: `duration`, default: `10s`)

Length of the timeout for reads.
It is expressed as a Golang time.Duration string, meaning a number followed
by a unit abbreviation (s = seconds,
m = minutes, h = hours), e.g., the default `10s` is ten seconds.
