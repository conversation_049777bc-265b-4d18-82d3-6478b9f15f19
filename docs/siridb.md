# TSBS Supplemental Guide: SiriDB

SiriDB is an open source time-series database with cluster support for scaling and redundancy. It is written in native C and the source code and documentation can be found on [GitHub](https://github.com/SiriDB/siridb-server). The [Go-SiriDB-Connector](https://github.com/SiriDB/go-siridb-connector) can be used to communicate with a single SiriDB server and a more advanced client is provided which can connect to multiple SiriDB servers so queries and inserts are balanced.

This supplemental guide explains how the data generated for TSBS is stored, additional flags available when using the data importer (`tsbs_load_siridb`), and additional flags available for the query runner (`tsbs_run_queries_siridb`).
**This should be read *after* the main README.**

## Data format

Data generated by `tsbs_generate_data` for SiriDB is serialized in such a way that a known number of bytes acting as a header indicate the number of bytes of content that will follow.

The serialized format is not (easily) human readable, but one line can be described in the following way:
```text
<number of metrics> <length of name and tags> <name and tags> <length of field key_1> <length of timestamp_1 and field value_1> <field key_1> <packed timestamp_1 and value_1> <length of field key_2> <length of timestamp_1 and field value_2> <field key_2> <packed timestamp_1 and value_2>... etc.

```
The measurement name, tags and field key will compose the SiriDB series name when the data is inserted. The timestamp and field value are packed with [Go-QPack](https://github.com/transceptor-technology/go-qpack). Go-QPack serializes the data in the right format for SiriDB.

---


## `tsbs_load_siridb` Additional Flags

### Database related

#### `-dbuser` (type: `string`, default: `iris`)
The database user to authenticate.


#### `-dbpass` (type: `string`, default: `siri`)
The database password to authenticate.


#### `-hosts` (type: `string`, default: `localhost:9000`)
The host name(s) of the SiriDB server(s). One or two hosts (comma separated) can be provided. Passing along two hosts will produce two pools if the replica flag is set to `false`. Make sure you have set up the SiriDB server(s) and corresponding configuration file(s) in advance. Having two pools means that the time series are distributed over two servers. It is however possible to create more than two pools, but SiriDB needs a moment to re-index the time series before another pool can be added. Therefore only a maximum of two hosts is supported in the benchmark suite. If two hosts are provided, two [single connections](https://github.com/SiriDB/go-siridb-connector#single-connection) are made. When there is only one worker active, the insert requests are sent to one server. When there are two workers active the insert requests are balanced over both servers.


#### `-replica` (type: `boolean`, default: `false`)
Whether to create a replica instead of a second pool.


#### `-write-timeout` (type: `int`, default: `10`)
Length of the timeout for writes.


### Miscellaneous

#### `-log-batches` (type: `boolean`, default: `false`)
Whether to time individual batches.



## `tsbs_run_queries_siridb` Additional Flags

### Database related

#### `-dbuser` (type: `string`, default: `iris`)
The database user to authenticate.

#### `-dbpass` (type: `string`, default: `siri`)
The database password to authenticate.

#### `-hosts` (type: `string`, default: `localhost:9000`)
A single host name or comma separated list of two host names for the SiriDB server(s). When you have two pools the [SiriDB client](https://github.com/SiriDB/go-siridb-connector#siridb-client) will balance the queries over both servers. In case of one pool where the second host is a replica, the client will send the query requests to only one server.

#### `-scale` (type: `uint64`, default: `8`)
The scale is important for creating the right groups within SiriDB. It is the number of hosts for which data has been generated. So it should be the same as the scale (number of hosts) of the inserted data. This number is used to create a group for every host.

#### `-query-limit` (type: `uint64`, default: `1000000`)
The query limit changes the maximum points which can be returned by a select query. The default and recommended value is set to one million points. This value is chosen to prevent a single query for taking to much memory and ensures SiriDB can respond to almost any query in a reasonable amount of time. But in case of a large number of hosts it might be needed to increase the query-limit.

#### `-write-timeout` (type: `int`, default: `10`)
Length of the timeout for writes.

### Miscellaneous

#### `-show-explain` (type: `boolean`, default: `false`)
Whether to print out a sample output for the first query in the set of queries. This will be the only query run and is useful for understanding the query plan that is being generated for a particular
query type.