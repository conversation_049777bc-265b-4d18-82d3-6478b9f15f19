language: go
dist: focal
jobs:
  include:
    - stage: test
      name: "Go 1.14"
      go:
        - 1.14.x
      install: skip
      script:
        - GO111MODULE=on go test -v -race -coverprofile=coverage.txt -covermode=atomic ./...
    - stage: test
      name: "Go 1.15"
      go:
        - 1.15.x
      install: skip
      script:
        - GO111MODULE=on go test -v -race -coverprofile=coverage.txt -covermode=atomic ./...
